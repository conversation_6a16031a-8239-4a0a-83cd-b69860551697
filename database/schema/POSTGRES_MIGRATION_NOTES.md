# SQLite to PostgreSQL Schema Migration Notes

## Overview
This document outlines the conversion of the SQLite schema (`sqlite-schema.sql`) to a PostgreSQL-compatible schema (`postgres-schema.sql`) for production deployment.

## Key Conversions Made

### 1. Auto-increment Primary Keys
- **SQLite**: `integer primary key autoincrement`
- **PostgreSQL**: `SERIAL PRIMARY KEY` (for regular tables) or `BIGSERIAL PRIMARY KEY` (for high-volume tables like jobs, failed_jobs)

### 2. Data Type Conversions

| SQLite Type | PostgreSQL Type | Notes |
|-------------|-----------------|-------|
| `tinyint(1)` | `BOOLEAN` | Boolean values, defaults converted to `true`/`false` |
| `datetime` | `TIMESTAMP` | Timestamp with timezone support |
| `date` | `DATE` | Date only |
| `time` | `TIME` | Time only, explicit format (HH:MM:SS) |
| `numeric` | `DECIMAL(10,2)` | Precision specified for monetary values |
| `varchar` | `VARCHAR(255)` | Length specification required in PostgreSQL |
| `text` | `TEXT` | Unlimited text |

### 3. Default Value Adjustments
- Boolean defaults: `'1'` → `true`, `'0'` → `false`
- Numeric defaults: Removed quotes around numeric values
- Time defaults: Added seconds component (`'08:00'` → `'08:00:00'`)

### 4. Constraint Modifications
- Check constraints: Maintained role validation with proper PostgreSQL syntax
- Foreign key constraints: Simplified syntax, removed `ON UPDATE NO ACTION` (PostgreSQL default)

### 5. Index Creation
- All indexes preserved with identical names and column specifications
- No syntax changes required for index creation

## Tables Converted

1. **migrations** - Migration tracking
2. **password_reset_tokens** - Password reset functionality
3. **sessions** - User session management
4. **cache** & **cache_locks** - Application caching
5. **jobs**, **job_batches**, **failed_jobs** - Queue management
6. **users** - User authentication and management
7. **fields** - Sports field/facility management
8. **amenities** - Field amenities
9. **utilities** - Field utilities with hourly rates
10. **bookings** - Reservation system
11. **field_amenity** - Many-to-many relationship (fields ↔ amenities)
12. **field_utility** - Many-to-many relationship (fields ↔ utilities)
13. **reservation_utility** - Utility usage tracking for reservations

## Migration Data Preservation
- All migration records from the original SQLite database are preserved
- Sequence reset command included to ensure proper auto-increment continuation
- Uses `ON CONFLICT (id) DO NOTHING` to prevent duplicate migration entries

## Production Deployment Considerations

### 1. Data Migration
If you have existing data in SQLite that needs to be migrated:

```bash
# Export data from SQLite
sqlite3 database.sqlite .dump > sqlite_data.sql

# Clean and convert the data dump for PostgreSQL
# (Remove SQLite-specific commands, adjust data types)

# Import into PostgreSQL
psql -d your_database -f postgres-schema.sql
psql -d your_database -f converted_data.sql
```

### 2. Application Configuration
Update your Laravel `.env` file for PostgreSQL:

```env
DB_CONNECTION=pgsql
DB_HOST=your_postgres_host
DB_PORT=5432
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 3. Laravel Migration Considerations
- The schema includes all migration records, so Laravel will recognize the current state
- Run `php artisan migrate:status` to verify migration state after deployment
- Consider running `php artisan migrate --pretend` first to see what Laravel would attempt

### 4. Performance Optimizations
- All original indexes are preserved
- Consider adding additional indexes based on production query patterns
- Monitor query performance and adjust as needed

### 5. Data Type Precision
- Monetary values use `DECIMAL(10,2)` for precise currency calculations
- Duration hours use `DECIMAL(4,2)` allowing up to 99.99 hours
- Phone numbers limited to `VARCHAR(20)` for international formats

## Testing Recommendations

1. **Schema Validation**: Test the schema creation on a clean PostgreSQL database
2. **Data Migration**: If migrating existing data, test the full migration process
3. **Application Testing**: Run your full test suite against the PostgreSQL database
4. **Performance Testing**: Compare query performance between SQLite and PostgreSQL

## Rollback Plan
- Keep the original SQLite database as backup
- Document any application-specific changes needed for PostgreSQL
- Test rollback procedures in a staging environment

## Notes
- The schema maintains 100% compatibility with the original SQLite structure
- All relationships, constraints, and indexes are preserved
- The conversion follows PostgreSQL best practices for data types and constraints
- UUID extension is commented out but available for future use if needed
