-- PostgreSQL Schema Conversion from SQLite
-- Generated for production PostgreSQL deployment
-- Maintains all tables, columns, constraints, indexes, and relationships from SQLite schema

-- Enable UUID extension for potential future use
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Migrations table
CREATE TABLE IF NOT EXISTS migrations (
    id SERIAL PRIMARY KEY,
    migration VARCHAR(255) NOT NULL,
    batch INTEGER NOT NULL
);

-- Password reset tokens table
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL,
    PRIMARY KEY (email)
);

-- Sessions table
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(255) NOT NULL,
    user_id INTEGER NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    payload TEXT NOT NULL,
    last_activity INTEGER NOT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX sessions_user_id_index ON sessions (user_id);
CREATE INDEX sessions_last_activity_index ON sessions (last_activity);

-- Cache table
CREATE TABLE IF NOT EXISTS cache (
    key VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    expiration INTEGER NOT NULL,
    PRIMARY KEY (key)
);

-- Cache locks table
CREATE TABLE IF NOT EXISTS cache_locks (
    key VARCHAR(255) NOT NULL,
    owner VARCHAR(255) NOT NULL,
    expiration INTEGER NOT NULL,
    PRIMARY KEY (key)
);

-- Jobs table
CREATE TABLE IF NOT EXISTS jobs (
    id BIGSERIAL PRIMARY KEY,
    queue VARCHAR(255) NOT NULL,
    payload TEXT NOT NULL,
    attempts INTEGER NOT NULL,
    reserved_at INTEGER NULL,
    available_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL
);

CREATE INDEX jobs_queue_index ON jobs (queue);

-- Job batches table
CREATE TABLE IF NOT EXISTS job_batches (
    id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    total_jobs INTEGER NOT NULL,
    pending_jobs INTEGER NOT NULL,
    failed_jobs INTEGER NOT NULL,
    failed_job_ids TEXT NOT NULL,
    options TEXT NULL,
    cancelled_at INTEGER NULL,
    created_at INTEGER NOT NULL,
    finished_at INTEGER NULL,
    PRIMARY KEY (id)
);

-- Failed jobs table
CREATE TABLE IF NOT EXISTS failed_jobs (
    id BIGSERIAL PRIMARY KEY,
    uuid VARCHAR(255) NOT NULL,
    connection TEXT NOT NULL,
    queue TEXT NOT NULL,
    payload TEXT NOT NULL,
    exception TEXT NOT NULL,
    failed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX failed_jobs_uuid_unique ON failed_jobs (uuid);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    username VARCHAR(255) NULL,
    role VARCHAR(20) CHECK (role IN ('admin', 'employee', 'user')) NOT NULL DEFAULT 'employee',
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    locked_until TIMESTAMP NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);

CREATE INDEX users_temp_role_index ON users (role);
CREATE INDEX users_temp_username_index ON users (username);
CREATE INDEX users_temp_failed_login_attempts_index ON users (failed_login_attempts);
CREATE UNIQUE INDEX users_temp_email_unique ON users (email);
CREATE UNIQUE INDEX users_temp_username_unique ON users (username);

-- Fields table
CREATE TABLE IF NOT EXISTS fields (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NULL,
    hourly_rate DECIMAL(10,2) NOT NULL,
    capacity INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Active',
    deleted_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    opening_time TIME NOT NULL DEFAULT '08:00:00',
    closing_time TIME NOT NULL DEFAULT '22:00:00',
    min_booking_hours DECIMAL(4,2) NOT NULL,
    max_booking_hours DECIMAL(4,2) NOT NULL,
    night_hourly_rate DECIMAL(10,2) NULL,
    night_time_start TIME NOT NULL DEFAULT '18:00:00'
);

CREATE INDEX fields_status_index ON fields (status);
CREATE INDEX fields_type_index ON fields (type);

-- Amenities table
CREATE TABLE IF NOT EXISTS amenities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    icon_class VARCHAR(100) NOT NULL DEFAULT 'ri-check-line',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL
);

CREATE INDEX amenities_is_active_name_index ON amenities (is_active, name);
CREATE INDEX amenities_created_at_index ON amenities (created_at);
CREATE UNIQUE INDEX amenities_name_unique ON amenities (name);

-- Utilities table
CREATE TABLE IF NOT EXISTS utilities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    icon_class VARCHAR(100) NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    hourly_rate DECIMAL(10,2) NULL
);

CREATE INDEX utilities_is_active_index ON utilities (is_active);
CREATE INDEX utilities_is_active_name_index ON utilities (is_active, name);
CREATE INDEX utilities_name_index ON utilities (name);
CREATE UNIQUE INDEX utilities_name_unique ON utilities (name);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id SERIAL PRIMARY KEY,
    field_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    booked_by INTEGER NULL,
    booking_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration_hours DECIMAL(4,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Pending',
    customer_name VARCHAR(255) NULL,
    customer_email VARCHAR(255) NULL,
    customer_phone VARCHAR(20) NULL,
    special_requests TEXT NULL,
    admin_notes TEXT NULL,
    confirmed_at TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (booked_by) REFERENCES users (id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (field_id) REFERENCES fields (id) ON DELETE CASCADE
);

CREATE INDEX booking_time_range_idx ON bookings (booking_date, start_time, end_time);
CREATE INDEX bookings_booking_date_start_time_index ON bookings (booking_date, start_time);
CREATE INDEX bookings_field_id_booking_date_index ON bookings (field_id, booking_date);
CREATE INDEX bookings_status_index ON bookings (status);
CREATE INDEX bookings_user_id_index ON bookings (user_id);
CREATE INDEX field_date_status_idx ON bookings (field_id, booking_date, status);

-- Field amenity pivot table
CREATE TABLE IF NOT EXISTS field_amenity (
    id SERIAL PRIMARY KEY,
    field_id INTEGER NOT NULL,
    amenity_id INTEGER NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (field_id) REFERENCES fields (id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenities (id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX field_amenity_field_id_amenity_id_unique ON field_amenity (field_id, amenity_id);
CREATE INDEX field_amenity_field_id_index ON field_amenity (field_id);
CREATE INDEX field_amenity_amenity_id_index ON field_amenity (amenity_id);

-- Field utility pivot table
CREATE TABLE IF NOT EXISTS field_utility (
    id SERIAL PRIMARY KEY,
    field_id INTEGER NOT NULL,
    utility_id INTEGER NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (field_id) REFERENCES fields (id) ON DELETE CASCADE,
    FOREIGN KEY (utility_id) REFERENCES utilities (id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX field_utility_field_id_utility_id_unique ON field_utility (field_id, utility_id);
CREATE INDEX field_utility_field_id_index ON field_utility (field_id);
CREATE INDEX field_utility_utility_id_index ON field_utility (utility_id);

-- Reservation utility table
CREATE TABLE IF NOT EXISTS reservation_utility (
    id SERIAL PRIMARY KEY,
    reservation_id INTEGER NOT NULL,
    utility_id INTEGER NOT NULL,
    hours INTEGER NOT NULL,
    rate DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (reservation_id) REFERENCES bookings (id) ON DELETE CASCADE,
    FOREIGN KEY (utility_id) REFERENCES utilities (id) ON DELETE CASCADE
);

-- Insert migration records
INSERT INTO migrations (id, migration, batch) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_06_18_205304_add_role_to_users_table', 1),
(5, '2025_06_19_005206_create_fields_table', 1),
(6, '2025_06_19_005224_create_bookings_table', 1),
(7, '2025_06_23_001000_add_fpmp_field_types', 1),
(8, '2025_06_23_201737_create_amenities_table', 1),
(9, '2025_06_23_201838_create_field_amenity_table', 1),
(10, '2025_06_23_224025_create_utilities_table', 1),
(11, '2025_06_23_224128_create_field_utility_table', 1),
(12, '2025_06_24_095839_add_hourly_rate_to_utilities_table', 1),
(13, '2025_06_24_101414_add_night_rate_to_fields_table', 1),
(14, '2025_06_24_102140_update_user_roles_terminology', 1),
(15, '2025_06_24_113459_make_icon_class_nullable_in_utilities_table', 1),
(16, '2025_06_24_124146_remove_amenities_column_from_fields_table', 1),
(17, '2025_07_10_182713_create_reservation_utility_table', 1),
(18, '2025_07_31_120000_update_duration_hours_to_decimal', 1)
ON CONFLICT (id) DO NOTHING;

-- Reset sequences to match the inserted data
SELECT setval('migrations_id_seq', (SELECT MAX(id) FROM migrations));
