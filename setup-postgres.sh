#!/bin/bash

# PostgreSQL Database Setup Script for SMP Online
# This script helps set up the PostgreSQL database for production deployment

echo "🚀 Setting up PostgreSQL database for SMP Online..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="smp_online"
DB_USER="smp_user"
DB_HOST="localhost"
DB_PORT="5432"

echo -e "${YELLOW}📋 Database Configuration:${NC}"
echo "  Database Name: $DB_NAME"
echo "  Database User: $DB_USER"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo ""

# Check if PostgreSQL is running
echo -e "${YELLOW}🔍 Checking PostgreSQL service...${NC}"
if ! pg_isready -h $DB_HOST -p $DB_PORT > /dev/null 2>&1; then
    echo -e "${RED}❌ PostgreSQL is not running or not accessible.${NC}"
    echo "Please start PostgreSQL service first:"
    echo "  macOS (Homebrew): brew services start postgresql"
    echo "  Ubuntu/Debian: sudo systemctl start postgresql"
    echo "  Windows: Start PostgreSQL service from Services panel"
    exit 1
fi
echo -e "${GREEN}✅ PostgreSQL is running${NC}"

# Function to create database and user
setup_database() {
    echo -e "${YELLOW}🗄️  Setting up database and user...${NC}"
    
    # Connect as postgres superuser to create database and user
    echo "Please enter the PostgreSQL superuser (postgres) password when prompted:"
    
    # Create user if not exists
    psql -h $DB_HOST -p $DB_PORT -U postgres -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$DB_USER') THEN
                CREATE USER $DB_USER WITH PASSWORD 'secure_password_123';
            END IF;
        END
        \$\$;
    " 2>/dev/null

    # Create database if not exists
    psql -h $DB_HOST -p $DB_PORT -U postgres -c "
        SELECT 'CREATE DATABASE $DB_NAME OWNER $DB_USER'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec
    " 2>/dev/null

    # Grant privileges
    psql -h $DB_HOST -p $DB_PORT -U postgres -c "
        GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
        ALTER USER $DB_USER CREATEDB;
    " 2>/dev/null

    echo -e "${GREEN}✅ Database and user setup complete${NC}"
}

# Function to apply schema
apply_schema() {
    echo -e "${YELLOW}📊 Applying PostgreSQL schema...${NC}"
    
    if [ ! -f "database/schema/postgres-schema.sql" ]; then
        echo -e "${RED}❌ Schema file not found: database/schema/postgres-schema.sql${NC}"
        exit 1
    fi
    
    # Apply the schema
    PGPASSWORD="secure_password_123" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f database/schema/postgres-schema.sql
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Schema applied successfully${NC}"
    else
        echo -e "${RED}❌ Failed to apply schema${NC}"
        exit 1
    fi
}

# Function to test connection
test_connection() {
    echo -e "${YELLOW}🔌 Testing Laravel database connection...${NC}"
    
    # Test with Laravel
    php artisan tinker --execute="
        try {
            \DB::connection()->getPdo();
            echo 'Database connection: SUCCESS\n';
            echo 'Database name: ' . \DB::connection()->getDatabaseName() . '\n';
        } catch (Exception \$e) {
            echo 'Database connection: FAILED\n';
            echo 'Error: ' . \$e->getMessage() . '\n';
        }
    "
}

# Function to run seeders
run_seeders() {
    echo -e "${YELLOW}🌱 Running database seeders...${NC}"
    
    php artisan db:seed
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Seeders completed successfully${NC}"
    else
        echo -e "${RED}❌ Seeders failed${NC}"
        exit 1
    fi
}

# Main execution
echo -e "${YELLOW}🎯 Starting database setup process...${NC}"
echo ""

# Step 1: Setup database and user
setup_database

# Step 2: Apply schema
apply_schema

# Step 3: Test connection
test_connection

# Step 4: Ask about seeders
echo ""
read -p "Do you want to run the database seeders now? (y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    run_seeders
fi

echo ""
echo -e "${GREEN}🎉 PostgreSQL setup complete!${NC}"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Update your .env file with these database credentials:"
echo "   DB_CONNECTION=pgsql"
echo "   DB_HOST=$DB_HOST"
echo "   DB_PORT=$DB_PORT"
echo "   DB_DATABASE=$DB_NAME"
echo "   DB_USERNAME=$DB_USER"
echo "   DB_PASSWORD=secure_password_123"
echo ""
echo "2. Clear Laravel caches:"
echo "   php artisan config:clear"
echo "   php artisan cache:clear"
echo ""
echo "3. Test your application!"
